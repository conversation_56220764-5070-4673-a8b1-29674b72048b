#!/usr/bin/env python3
"""
Script pour créer la table tcompanies et migrer les données
"""
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def create_tcompanies_table():
    """Créer la table tcompanies et migrer les données"""
    
    async with engine.begin() as conn:
        try:
            print("🔄 Création de la table tcompanies...")
            
            # 1. Créer la table tcompanies
            await conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tcompanies (
                    id SERIAL PRIMARY KEY,
                    company_name VARCHAR(255) NOT NULL,
                    activity VARCHAR(255),
                    address TEXT,
                    postal_code VARCHAR(10),
                    city VARCHAR(100),
                    country VARCHAR(100) DEFAULT 'France',
                    phone VARCHAR(20),
                    fax VARCHAR(20),
                    email VARCHAR(320),
                    siret VARCHAR(14),
                    vat_number VARCHAR(20),
                    legal_representative_id INTEGER REFERENCES users(id),
                    logo_url VARCHAR(500),
                    logo_filename VARCHAR(255),
                    workspace_id INTEGER NOT NULL REFERENCES workspaces(id),
                    is_active BOOLEAN NOT NULL DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    created_by INTEGER REFERENCES users(id),
                    
                    CONSTRAINT unique_tcompany_siret UNIQUE (siret),
                    CONSTRAINT unique_workspace_tcompany_name UNIQUE (workspace_id, company_name)
                )
            """))
            print("✅ Table tcompanies créée")
            
            # 2. Créer les index
            await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_tcompanies_company_name ON tcompanies (company_name)"))
            await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_tcompanies_email ON tcompanies (email)"))
            await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_tcompanies_siret ON tcompanies (siret)"))
            await conn.execute(text("CREATE INDEX IF NOT EXISTS ix_tcompanies_workspace_id ON tcompanies (workspace_id)"))
            print("✅ Index créés")
            
            # 3. Migrer les données de entreprises_tiers vers tcompanies
            print("📋 Migration des données...")
            result = await conn.execute(text("""
                INSERT INTO tcompanies (
                    id, company_name, activity, address, postal_code, city, country,
                    phone, fax, email, siret, vat_number, legal_representative_id,
                    logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                )
                SELECT 
                    id, nom_entreprise, activite, adresse, code_postal, ville, pays,
                    telephone, fax, email, siret, tva_intracommunautaire, representant_legal_id,
                    logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                FROM entreprises_tiers
                ON CONFLICT (id) DO NOTHING
            """))
            print(f"✅ Données migrées")
            
            # 4. Mettre à jour la séquence
            await conn.execute(text("""
                SELECT setval('tcompanies_id_seq', (SELECT MAX(id) FROM tcompanies))
            """))
            print("✅ Séquence mise à jour")
            
            # 5. Ajouter la colonne tcompany_id dans employees si elle n'existe pas
            print("🔗 Mise à jour de la table employees...")
            await conn.execute(text("""
                ALTER TABLE employees 
                ADD COLUMN IF NOT EXISTS tcompany_id INTEGER REFERENCES tcompanies(id)
            """))
            
            # Copier les données
            await conn.execute(text("""
                UPDATE employees 
                SET tcompany_id = entreprise_tiers_id 
                WHERE entreprise_tiers_id IS NOT NULL AND tcompany_id IS NULL
            """))
            print("✅ Table employees mise à jour")
            
            # 6. Vérifier les résultats
            result = await conn.execute(text("SELECT COUNT(*) FROM tcompanies"))
            count = result.fetchone()[0]
            print(f"📊 Nombre d'enregistrements dans tcompanies: {count}")
            
            print("🎉 Migration terminée avec succès!")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(create_tcompanies_table())
