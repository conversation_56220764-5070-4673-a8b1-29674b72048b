# app/models/lot.py
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class LotPhase(str, enum.Enum):
    ESQ = "ESQ"      # Esquisse
    APD = "APD"      # Avant-Projet Détaillé
    PRODCE = "PRODCE" # Projet de Conception et d'Exécution
    EXE = "EXE"      # Exécution

class Lot(Base):
    __tablename__ = "lots"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    
    # Relation obligatoire avec le projet
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    
    # Phase actuelle du lot
    current_phase = Column(Enum(LotPhase, values_callable=lambda obj: [e.value for e in obj]), default=LotPhase.ESQ)
    
    # Historique des phases validées
    esq_validated = Column(Boolean, default=False)
    esq_validated_at = Column(DateTime)
    esq_validated_by = Column(Integer, ForeignKey("users.id"))
    
    apd_validated = Column(Boolean, default=False)
    apd_validated_at = Column(DateTime)
    apd_validated_by = Column(Integer, ForeignKey("users.id"))
    
    prodce_validated = Column(Boolean, default=False)
    prodce_validated_at = Column(DateTime)
    prodce_validated_by = Column(Integer, ForeignKey("users.id"))
    
    exe_validated = Column(Boolean, default=False)
    exe_validated_at = Column(DateTime)
    exe_validated_by = Column(Integer, ForeignKey("users.id"))
    
    # Métadonnées
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Relations
    project = relationship("Project", back_populates="lots")
    workspace = relationship("Workspace", back_populates="lots")
    creator = relationship("User", foreign_keys=[created_by])
    
    # Relations pour les validateurs de phases
    esq_validator = relationship("User", foreign_keys=[esq_validated_by])
    apd_validator = relationship("User", foreign_keys=[apd_validated_by])
    prodce_validator = relationship("User", foreign_keys=[prodce_validated_by])
    exe_validator = relationship("User", foreign_keys=[exe_validated_by])
    
    # Relations avec les intervenants et documents
    intervenants = relationship("LotIntervenant", back_populates="lot", cascade="all, delete-orphan")
    documents = relationship("LotDocument", back_populates="lot", cascade="all, delete-orphan")
    technical_documents = relationship("TechnicalDocument", back_populates="lot")

    def __repr__(self):
        return f"<Lot(id={self.id}, name='{self.name}', code='{self.code}', phase='{self.current_phase}')>"

    @property
    def phase_progress(self) -> dict:
        """Retourne le progrès des phases sous forme de dictionnaire"""
        return {
            "ESQ": self.esq_validated,
            "APD": self.apd_validated,
            "PRODCE": self.prodce_validated,
            "EXE": self.exe_validated
        }

    @property
    def next_phase(self) -> LotPhase:
        """Retourne la prochaine phase à valider"""
        if not self.esq_validated:
            return LotPhase.ESQ
        elif not self.apd_validated:
            return LotPhase.APD
        elif not self.prodce_validated:
            return LotPhase.PRODCE
        elif not self.exe_validated:
            return LotPhase.EXE
        return None

    def can_validate_phase(self, phase: LotPhase) -> bool:
        """Vérifie si une phase peut être validée"""
        if phase == LotPhase.ESQ:
            return True
        elif phase == LotPhase.APD:
            return self.esq_validated
        elif phase == LotPhase.PRODCE:
            return self.apd_validated
        elif phase == LotPhase.EXE:
            return self.prodce_validated
        return False

class LotIntervenant(Base):
    __tablename__ = "lot_intervenants"
    
    id = Column(Integer, primary_key=True, index=True)
    lot_id = Column(Integer, ForeignKey("lots.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("tcompanies.id"), nullable=False)
    role = Column(String(100))  # Architecte, Bureau d'études, Entreprise générale, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Relations
    lot = relationship("Lot", back_populates="intervenants")
    company = relationship("TCompany")
    creator = relationship("User")

    def __repr__(self):
        return f"<LotIntervenant(id={self.id}, lot_id={self.lot_id}, company_id={self.company_id}, role='{self.role}')>"

class LotDocument(Base):
    __tablename__ = "lot_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    lot_id = Column(Integer, ForeignKey("lots.id"), nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    phase = Column(Enum(LotPhase, values_callable=lambda obj: [e.value for e in obj]))  # Phase à laquelle appartient le document
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"))
    
    # Relations
    lot = relationship("Lot", back_populates="documents")
    document = relationship("Document")
    creator = relationship("User")

    def __repr__(self):
        return f"<LotDocument(id={self.id}, lot_id={self.lot_id}, document_id={self.document_id}, phase='{self.phase}')>"
