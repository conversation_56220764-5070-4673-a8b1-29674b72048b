'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import TCompanyModal from '@/components/TCompanyModal'
import { PlusIcon, MagnifyingGlassIcon, BuildingOfficeIcon, PhoneIcon, EnvelopeIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'

interface TCompany {
  id: number
  company_name: string
  activity?: string
  address?: string
  postal_code?: string
  city?: string
  country?: string
  phone?: string
  fax?: string
  email?: string
  siret?: string
  vat_number?: string
  legal_representative_name?: string
  legal_representative_email?: string
  logo_url?: string
  logo_filename?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface TCompanyFormData {
  company_name: string
  activity: string
  address: string
  postal_code: string
  city: string
  country: string
  phone: string
  fax: string
  email: string
  siret: string
  vat_number: string
  logo_url?: string
  logo_filename?: string
}

export default function TCompaniesPage() {
  const { user, signOut } = useAuth()
  const [tcompanies, setTCompanies] = useState<TCompany[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedTCompany, setSelectedTCompany] = useState<TCompany | null>(null)
  const [formData, setFormData] = useState<TCompanyFormData>({
    company_name: '',
    activity: '',
    address: '',
    postal_code: '',
    city: '',
    country: 'France',
    phone: '',
    fax: '',
    email: '',
    siret: '',
    vat_number: '',
    logo_url: '',
    logo_filename: ''
  })

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Charger les TCompanies
  const fetchTCompanies = async () => {
    try {
      setLoading(true)
      const response = await BusinessDataService.makeAuthenticatedRequest('/tcompanies/')

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des TCompanies')
      }

      const data = await response.json()
      setTCompanies(Array.isArray(data) ? data : [])
      setError(null)
    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors du chargement des TCompanies')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTCompanies()
  }, [])

  // Filtrer les TCompanies
  const filteredTCompanies = tcompanies.filter(tcompany =>
    tcompany.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tcompany.email && tcompany.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (tcompany.city && tcompany.city.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (tcompany.siret && tcompany.siret.includes(searchTerm))
  )

  // Gérer la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const endpoint = selectedTCompany
        ? `/tcompanies/${selectedTCompany.id}`
        : '/tcompanies/'

      const method = selectedTCompany ? 'PUT' : 'POST'

      const response = await BusinessDataService.makeAuthenticatedRequest(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la sauvegarde')
      }

      // Recharger la liste
      await fetchTCompanies()

      // Fermer les modales et réinitialiser
      setIsCreateModalOpen(false)
      setIsEditModalOpen(false)
      setSelectedTCompany(null)
      resetForm()

    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors de la sauvegarde')
    }
  }

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      company_name: '',
      activity: '',
      address: '',
      postal_code: '',
      city: '',
      country: 'France',
      phone: '',
      fax: '',
      email: '',
      siret: '',
      vat_number: '',
      logo_url: '',
      logo_filename: ''
    })
  }

  // Ouvrir le modal d'édition
  const handleEdit = (tcompany: TCompany) => {
    setSelectedTCompany(tcompany)
    setFormData({
      company_name: tcompany.company_name || '',
      activity: tcompany.activity || '',
      address: tcompany.address || '',
      postal_code: tcompany.postal_code || '',
      city: tcompany.city || '',
      country: tcompany.country || 'France',
      phone: tcompany.phone || '',
      fax: tcompany.fax || '',
      email: tcompany.email || '',
      siret: tcompany.siret || '',
      vat_number: tcompany.vat_number || '',
      logo_url: tcompany.logo_url || '',
      logo_filename: tcompany.logo_filename || ''
    })
    setIsEditModalOpen(true)
  }

  // Ouvrir le modal de création
  const handleCreate = () => {
    resetForm()
    setSelectedTCompany(null)
    setIsCreateModalOpen(true)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-ton4">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Carnet d'adresses"
                subtitle="Chargement des entreprises..."
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Chargement des entreprises...</p>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-ton4">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Carnet d'adresses"
                subtitle="Erreur de chargement"
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={fetchEntreprises}>Réessayer</Button>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-ton4">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Carnet d'adresses"
              subtitle={`Gestion de vos TCompanies • ${filteredTCompanies.length} entreprise${filteredTCompanies.length > 1 ? 's' : ''}`}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

            <main className="p-6">
              {/* Barre d'actions */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une entreprise..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <Button onClick={handleCreate} className="flex items-center gap-2">
                  <PlusIcon className="h-5 w-5" />
                  Nouvelle entreprise
                </Button>
              </div>

              {/* Liste des TCompanies */}
              {filteredTCompanies.length === 0 ? (
                <div className="text-center py-12">
                  <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune entreprise</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm ? 'Aucune entreprise ne correspond à votre recherche.' : 'Commencez par ajouter une TCompany.'}
                  </p>
                  {!searchTerm && (
                    <div className="mt-6">
                      <Button onClick={handleCreate}>
                        <PlusIcon className="h-5 w-5 mr-2" />
                        Ajouter une entreprise
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTCompanies.map((tcompany) => (
                    <Card key={tcompany.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleEdit(tcompany)}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          {/* Logo de l'entreprise */}
                          {tcompany.logo_url ? (
                            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                              <Image
                                src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${tcompany.logo_url}`}
                                alt={`Logo ${tcompany.company_name}`}
                                width={48}
                                height={48}
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  console.error('Erreur de chargement du logo:', tcompany.logo_url)
                                }}
                              />
                            </div>
                          ) : (
                            <BuildingOfficeIcon className="h-12 w-12 text-primary-600 flex-shrink-0" />
                          )}

                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {tcompany.company_name}
                            </h3>
                            {tcompany.activity && (
                              <p className="text-sm text-gray-600 mb-3">{tcompany.activity}</p>
                            )}
                          </div>
                        </div>
                        <div className={`px-2 py-1 text-xs rounded-full ${
                          tcompany.is_active
                            ? 'bg-ton3 text-ton1'
                            : 'bg-ton3-dark text-ton2'
                        }`}>
                          {tcompany.is_active ? 'Actif' : 'Inactif'}
                        </div>
                      </div>

                      <div className="space-y-2">
                        {tcompany.address && (
                          <div className="text-sm text-gray-600">
                            <p>{tcompany.address}</p>
                            {(tcompany.postal_code || tcompany.city) && (
                              <p>{tcompany.postal_code} {tcompany.city}</p>
                            )}
                          </div>
                        )}

                        {tcompany.phone && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <PhoneIcon className="h-4 w-4" />
                            <span>{tcompany.phone}</span>
                          </div>
                        )}

                        {tcompany.email && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <EnvelopeIcon className="h-4 w-4" />
                            <span>{tcompany.email}</span>
                          </div>
                        )}

                        {tcompany.siret && (
                          <div className="text-xs text-gray-500">
                            SIRET: {tcompany.siret}
                          </div>
                        )}

                        {tcompany.legal_representative_name && (
                          <div className="text-xs text-gray-500">
                            Représentant: {tcompany.legal_representative_name}
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </main>
          </div>
        </div>

        {/* Modales */}
        <TCompanyModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={false}
        />

        <TCompanyModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={true}
          tcompanyId={selectedTCompany?.id}
          onRefresh={fetchTCompanies}
        />
      </div>
    </ProtectedRoute>
  )
}
