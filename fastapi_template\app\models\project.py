# app/models/project.py
from sqlalchemy import <PERSON>olean, Column, Integer, String, DateTime, ForeignKey, Text, Enum, Numeric, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class ProjectStatus(str, enum.Enum):
    EN_COURS = "EN_COURS"
    EN_ATTENTE = "EN_ATTENTE"
    TERMINE = "TERMINE"
    ARCHIVE = "ARCHIVE"

class ProjectNature(str, enum.Enum):
    DEVIS = "DEVIS"
    AO = "AO"
    AFFAIRE = "AFFAIRE"

class ProjectCompany(Base):
    """Table de liaison entre projets et entreprises"""
    __tablename__ = "project_company"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("tcompanies.id"), nullable=False)
    role = Column(String(50), nullable=False, default="OWNER")  # OWNER, PARTNER, SUBCONTRACTOR
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = relationship("Project", back_populates="companies")
    company = relationship("TCompany", back_populates="project_associations")
    tcompany = relationship("TCompany", back_populates="project_associations")

    # Contrainte unique
    __table_args__ = (
        UniqueConstraint('project_id', 'company_id', name='unique_project_company'),
    )

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    status = Column(Enum(ProjectStatus, values_callable=lambda obj: [e.value for e in obj]), default=ProjectStatus.EN_COURS)
    nature = Column(Enum(ProjectNature, values_callable=lambda obj: [e.value for e in obj]), default=ProjectNature.DEVIS)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    budget_total = Column(Numeric(15, 2))
    address = Column(Text)
    client_name = Column(String)
    client_contact = Column(String)
    workspace_id = Column(Integer, ForeignKey("workspaces.id"), nullable=False)
    is_archived = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    workspace = relationship("Workspace", back_populates="projects")
    companies = relationship("ProjectCompany", back_populates="project")
    documents = relationship("ProjectDocument", back_populates="project")
    employees = relationship("ProjectEmployee", back_populates="project")
    budgets = relationship("Budget", back_populates="project")
    purchase_orders = relationship("PurchaseOrder", back_populates="project")
    quotes = relationship("Quote", back_populates="project")
    time_entries = relationship("TimeEntry", back_populates="project")

    @property
    def primary_company(self):
        """Retourne l'entreprise propriétaire du projet"""
        for pc in self.companies:
            if pc.role == "OWNER" and pc.is_active:
                return pc.company
        return None

    @property
    def primary_company_id(self):
        """Retourne l'ID de l'entreprise propriétaire du projet"""
        primary = self.primary_company
        return primary.id if primary else None

class ProjectDocument(Base):
    __tablename__ = "project_documents"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    folder_type = Column(String)  # DCE, DAO, DPGF, FT, EXE, etc.
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    project = relationship("Project", back_populates="documents")
    document = relationship("Document")

class ProjectEmployee(Base):
    __tablename__ = "project_employees"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    role = Column(String)
    hourly_rate = Column(Numeric(10, 2))
    assigned_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Relationships
    project = relationship("Project", back_populates="employees")
    employee = relationship("Employee", back_populates="project_assignments")