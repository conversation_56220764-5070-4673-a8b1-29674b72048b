# Analyse Complète : Migration EntrepriseTiers → TCompanies

## 📊 Résultats de l'Analyse

### 🔍 Références Trouvées
- **Backend** : 167 références dans 25 fichiers Python
- **Frontend** : 0 référence (pas encore implémenté)

## 📁 Fichiers Impactés par Catégorie

### 1. Modèles SQLAlchemy (4 fichiers)
- `app/models/entreprise_tiers.py` ⭐ **PRINCIPAL**
- `app/models/project.py` (relations ProjectCompany)
- `app/models/document.py` (relations TechnicalDocumentCompany)
- `app/models/employee.py` (foreign key)
- `app/models/workspace.py` (relation back_populates)
- `app/models/__init__.py` (imports)

### 2. Schémas Pydantic (2 fichiers)
- `app/schemas/entreprise_tiers.py` ⭐ **PRINCIPAL**
- `app/schemas/project.py` (référence entreprise_tiers_id)

### 3. API Endpoints (3 fichiers)
- `app/api/api_v1/endpoints/entreprises_tiers.py` ⭐ **PRINCIPAL**
- `app/api/api_v1/endpoints/technical_documents.py` (utilise EntrepriseTiers)
- `app/api/api_v1/endpoints/projects_crud.py` (référence entreprise_tiers_id)
- `app/api/api_v1/api.py` (router)

### 4. Migrations Alembic (3 fichiers)
- `alembic/versions/add_entreprises_tiers.py` ⭐ **CRÉATION TABLE**
- `alembic/versions/add_logo_to_entreprise_tiers.py` (ajout colonnes logo)
- `alembic/versions/rename_companies_to_workspaces.py` (mise à jour workspace_id)

### 5. Scripts et Utilitaires (6 fichiers)
- `scripts/create_sample_entreprises_tiers.py`
- `scripts/create_test_entreprises_tiers.py`
- `scripts/test_technical_document_creation.py`
- `run_migration.py`
- `verify_migration.py`

### 6. Tests (1 fichier)
- `tests_crud/test_entreprise_tiers_crud.py` ⭐ **TESTS CRUD**

## 🎯 Plan de Migration Détaillé

### Phase 1 : Nouveau Modèle TCompany
```python
# Nouveau fichier : app/models/tcompany.py
class TCompany(Base):
    __tablename__ = "tcompanies"
    
    # Colonnes standardisées en anglais
    company_name = Column(String(255), nullable=False, index=True)
    activity = Column(String(255), nullable=True)
    address = Column(Text, nullable=True)
    postal_code = Column(String(10), nullable=True)
    city = Column(String(100), nullable=True)
    country = Column(String(100), default="France")
    phone = Column(String(20), nullable=True)
    email = Column(String(320), nullable=True, index=True)
    siret = Column(String(14), nullable=True, index=True)
    vat_number = Column(String(20), nullable=True)
    
    # Contraintes
    __table_args__ = (
        UniqueConstraint('siret', name='unique_siret'),
        UniqueConstraint('workspace_id', 'company_name', name='unique_workspace_company'),
    )
```

### Phase 2 : Migration Base de Données
```sql
-- 1. Renommer la table
ALTER TABLE entreprises_tiers RENAME TO tcompanies;

-- 2. Renommer les colonnes
ALTER TABLE tcompanies RENAME COLUMN nom_entreprise TO company_name;
ALTER TABLE tcompanies RENAME COLUMN activite TO activity;
ALTER TABLE tcompanies RENAME COLUMN adresse TO address;
ALTER TABLE tcompanies RENAME COLUMN code_postal TO postal_code;
ALTER TABLE tcompanies RENAME COLUMN ville TO city;
ALTER TABLE tcompanies RENAME COLUMN pays TO country;
ALTER TABLE tcompanies RENAME COLUMN telephone TO phone;
ALTER TABLE tcompanies RENAME COLUMN representant_legal_id TO legal_representative_id;
ALTER TABLE tcompanies RENAME COLUMN tva_intracommunautaire TO vat_number;

-- 3. Ajouter contraintes
ALTER TABLE tcompanies ADD CONSTRAINT unique_siret UNIQUE (siret);
ALTER TABLE tcompanies ADD CONSTRAINT unique_workspace_company UNIQUE (workspace_id, company_name);
```

### Phase 3 : Nouveaux Schémas Pydantic
```python
# Nouveau fichier : app/schemas/tcompany.py
class TCompanyCreate(BaseModel):
    company_name: str = Field(..., min_length=1, max_length=255)
    activity: Optional[str] = Field(None, max_length=255)
    siret: Optional[str] = Field(None, regex=r'^\d{14}$')
    phone: Optional[str] = Field(None, regex=r'^(\+33|0)[1-9](\d{8})$')
    postal_code: Optional[str] = Field(None, regex=r'^\d{5}$')
    email: Optional[EmailStr] = None
```

### Phase 4 : Nouveaux Endpoints
```python
# Nouveau fichier : app/api/api_v1/endpoints/tcompanies.py
@router.get("/", response_model=List[TCompanyResponse])
async def get_tcompanies(...):
    # Logique identique mais avec TCompany
```

## 🔄 Ordre d'Exécution Recommandé

### Étape 1 : Préparation (✅ TERMINÉ)
- [x] Analyse des références
- [x] Identification des fichiers impactés
- [x] Plan détaillé

### Étape 2 : Nouveau Modèle
1. Créer `app/models/tcompany.py`
2. Mettre à jour `app/models/__init__.py`
3. Créer migration Alembic pour la nouvelle table

### Étape 3 : Nouveaux Schémas
1. Créer `app/schemas/tcompany.py` avec validation française
2. Tests de validation Pydantic

### Étape 4 : Nouveaux Endpoints
1. Créer `app/api/api_v1/endpoints/tcompanies.py`
2. Mettre à jour `app/api/api_v1/api.py`
3. Tests API

### Étape 5 : Migration des Relations
1. Mettre à jour `ProjectCompany.company_id` → référence `TCompany`
2. Mettre à jour `TechnicalDocumentCompany.company_id` → référence `TCompany`
3. Mettre à jour `Employee.entreprise_tiers_id` → `tcompany_id`

### Étape 6 : Migration des Données
1. Exécuter migration Alembic
2. Vérifier intégrité des données
3. Tests complets

### Étape 7 : Nettoyage
1. Supprimer anciens fichiers
2. Nettoyer imports
3. Documentation

## ⚠️ Points d'Attention

### Contraintes de Foreign Key
- `project_company.company_id` → doit pointer vers `tcompanies.id`
- `technical_document_companies.company_id` → doit pointer vers `tcompanies.id`
- `employees.entreprise_tiers_id` → renommer en `tcompany_id`

### Validation Française
- **SIRET** : 14 chiffres exactement
- **Téléphone** : Format français (+33 ou 0)
- **Code postal** : 5 chiffres
- **Email** : Validation RFC 5321

### Tests Critiques
- Isolation multi-tenant
- Contraintes d'unicité
- Relations foreign key
- Validation Pydantic

## 🚀 Prêt pour l'Étape 2 ?

Voulez-vous que je commence par créer le **nouveau modèle TCompany** avec toutes les améliorations ?
