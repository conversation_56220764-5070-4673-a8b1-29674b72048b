'use client'

import { Modal } from '@/components/ui/Modal'
import { Input } from '@/components/ui/Input'
import { But<PERSON> } from '@/components/ui/Button'
import LogoUpload from '@/components/entreprises/LogoUpload'
import { useTCompanyLogo } from '@/hooks/useTCompanyLogo'

interface TCompanyFormData {
  company_name: string
  activity: string
  address: string
  postal_code: string
  city: string
  country: string
  phone: string
  fax: string
  email: string
  siret: string
  vat_number: string
  logo_url?: string
  logo_filename?: string
}

interface TCompanyModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (e: React.FormEvent) => void
  formData: TCompanyFormData
  setFormData: (data: TCompanyFormData) => void
  isEdit?: boolean
  loading?: boolean
  tcompanyId?: number | null
  onRefresh?: () => void
}

export default function TCompanyModal({
  isOpen,
  onClose,
  onSubmit,
  formData,
  setFormData,
  isEdit = false,
  loading = false,
  entrepriseId = null,
  onRefresh
}: TCompanyModalProps) {
  
  // Hook pour gérer le logo (seulement en mode édition)
  const logoHook = entrepriseId ? useEntrepriseLogo({
    entrepriseId,
    onSuccess: () => {
      if (onRefresh) {
        onRefresh()
      }
    }
  }) : null

  const handleInputChange = (field: keyof TCompanyFormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    })
  }

  const handleLogoUpload = async (file: File) => {
    if (logoHook && entrepriseId) {
      await logoHook.uploadLogo(file)
    }
  }

  const handleLogoDelete = async () => {
    if (logoHook && entrepriseId) {
      await logoHook.deleteLogo()
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Modifier la TCompany' : 'Nouvelle TCompany'}
      size="lg"
    >
      <form onSubmit={onSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Nom de l'entreprise */}
          <div className="md:col-span-2">
            <Input
              label="Nom de l'entreprise *"
              value={formData.company_name}
              onChange={(e) => handleInputChange('company_name', e.target.value)}
              placeholder="Nom de l'entreprise"
              required
            />
          </div>

          {/* Activité */}
          <div className="md:col-span-2">
            <Input
              label="Activité"
              value={formData.activity}
              onChange={(e) => handleInputChange('activity', e.target.value)}
              placeholder="Secteur d'activité"
            />
          </div>

          {/* Adresse */}
          <div className="md:col-span-2">
            <Input
              label="Adresse"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Adresse complète"
            />
          </div>

          {/* Code postal */}
          <div>
            <Input
              label="Code postal"
              value={formData.postal_code}
              onChange={(e) => handleInputChange('postal_code', e.target.value)}
              placeholder="Code postal"
            />
          </div>

          {/* Ville */}
          <div>
            <Input
              label="Ville"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder="Ville"
            />
          </div>

          {/* Pays */}
          <div>
            <Input
              label="Pays"
              value={formData.country}
              onChange={(e) => handleInputChange('country', e.target.value)}
              placeholder="Pays"
            />
          </div>

          {/* Téléphone */}
          <div>
            <Input
              label="Téléphone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="Numéro de téléphone"
            />
          </div>

          {/* Fax */}
          <div>
            <Input
              label="Fax"
              value={formData.fax}
              onChange={(e) => handleInputChange('fax', e.target.value)}
              placeholder="Numéro de fax"
            />
          </div>

          {/* Email */}
          <div>
            <Input
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Adresse email"
            />
          </div>

          {/* SIRET */}
          <div>
            <Input
              label="SIRET"
              value={formData.siret}
              onChange={(e) => handleInputChange('siret', e.target.value)}
              placeholder="Numéro SIRET"
            />
          </div>

          {/* TVA Intracommunautaire */}
          <div>
            <Input
              label="TVA Intracommunautaire"
              value={formData.vat_number}
              onChange={(e) => handleInputChange('vat_number', e.target.value)}
              placeholder="Numéro de TVA"
            />
          </div>
        </div>

        {/* Section Logo (seulement en mode édition) */}
        {isEdit && entrepriseId && logoHook && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Logo de l'entreprise</h3>
            <LogoUpload
              currentLogoUrl={logoHook.logoUrl}
              onUpload={handleLogoUpload}
              onDelete={handleLogoDelete}
              loading={logoHook.loading}
              error={logoHook.error}
            />
          </div>
        )}

        {/* Boutons d'action */}
        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            loading={loading}
          >
            {isEdit ? 'Modifier' : 'Créer'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
