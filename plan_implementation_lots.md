# Plan d'Implémentation - Notion de Lot dans ORBIS

## Vue d'ensemble
Ajout de la notion de "Lot" comme entité intermédiaire entre les projets et les documents, avec gestion des phases et des intervenants.

## Architecture Actuelle Analysée

### Modèles existants pertinents :
- **Project** : Entité principale des projets
- **TCompany** : Entreprises tierces (remplace EntrepriseTiers)
- **TechnicalDocument** : Documents techniques liés aux projets
- **ProjectCompany** : Relation many-to-many entre projets et entreprises
- **TechnicalDocumentCompany** : Relation entre documents techniques et entreprises

### Structure Frontend :
- Interface React/Next.js avec composants modulaires
- Gestion des permissions via hooks
- API calls centralisées dans lib/api.ts

## Plan d'Implémentation

### Phase 1 : Modèles et Base de Données

#### 1.1 Création du modèle Lot
**Fichier** : `fastapi_template/app/models/lot.py`

```python
class LotPhase(str, enum.Enum):
    ESQ = "ESQ"      # Esquisse
    APD = "APD"      # Avant-Projet Détaillé
    PRODCE = "PRODCE" # Projet de Conception et d'Exécution
    EXE = "EXE"      # Exécution

class Lot(Base):
    __tablename__ = "lots"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
