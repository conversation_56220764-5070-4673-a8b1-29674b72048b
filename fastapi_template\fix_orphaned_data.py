#!/usr/bin/env python3
"""
Script pour nettoyer les données orphelines avant la migration
"""
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def fix_orphaned_data():
    """Nettoyer les données orphelines"""
    
    async with engine.begin() as conn:
        try:
            print("🔍 Vérification des données orphelines...")
            
            # 1. Vérifier les données dans technical_document_companies
            result = await conn.execute(text("""
                SELECT DISTINCT tdc.company_id 
                FROM technical_document_companies tdc
                LEFT JOIN tcompanies tc ON tdc.company_id = tc.id
                WHERE tc.id IS NULL
            """))
            orphaned_ids = [row[0] for row in result.fetchall()]
            
            if orphaned_ids:
                print(f"❌ IDs orphelins dans technical_document_companies: {orphaned_ids}")
                
                # Vérifier s'ils existent dans entreprises_tiers
                for company_id in orphaned_ids:
                    result = await conn.execute(text("""
                        SELECT nom_entreprise FROM entreprises_tiers WHERE id = :id
                    """), {"id": company_id})
                    entreprise = result.fetchone()
                    
                    if entreprise:
                        print(f"📋 ID {company_id} existe dans entreprises_tiers: {entreprise[0]}")
                        # Ajouter cette entreprise à tcompanies
                        await conn.execute(text("""
                            INSERT INTO tcompanies (
                                id, company_name, activity, address, postal_code, city, country,
                                phone, fax, email, siret, vat_number, legal_representative_id,
                                logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                            )
                            SELECT 
                                id, nom_entreprise, activite, adresse, code_postal, ville, pays,
                                telephone, fax, email, siret, tva_intracommunautaire, representant_legal_id,
                                logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                            FROM entreprises_tiers
                            WHERE id = :id
                            ON CONFLICT (id) DO NOTHING
                        """), {"id": company_id})
                        print(f"✅ Entreprise {company_id} ajoutée à tcompanies")
                    else:
                        print(f"❌ ID {company_id} n'existe nulle part - suppression des références")
                        # Supprimer les références orphelines
                        await conn.execute(text("""
                            DELETE FROM technical_document_companies WHERE company_id = :id
                        """), {"id": company_id})
                        print(f"✅ Références orphelines supprimées pour ID {company_id}")
            else:
                print("✅ Aucune donnée orpheline dans technical_document_companies")
            
            # 2. Vérifier project_company
            result = await conn.execute(text("""
                SELECT DISTINCT pc.company_id 
                FROM project_company pc
                LEFT JOIN tcompanies tc ON pc.company_id = tc.id
                WHERE tc.id IS NULL
            """))
            orphaned_ids = [row[0] for row in result.fetchall()]
            
            if orphaned_ids:
                print(f"❌ IDs orphelins dans project_company: {orphaned_ids}")
                
                for company_id in orphaned_ids:
                    result = await conn.execute(text("""
                        SELECT nom_entreprise FROM entreprises_tiers WHERE id = :id
                    """), {"id": company_id})
                    entreprise = result.fetchone()
                    
                    if entreprise:
                        print(f"📋 ID {company_id} existe dans entreprises_tiers: {entreprise[0]}")
                        # Ajouter cette entreprise à tcompanies
                        await conn.execute(text("""
                            INSERT INTO tcompanies (
                                id, company_name, activity, address, postal_code, city, country,
                                phone, fax, email, siret, vat_number, legal_representative_id,
                                logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                            )
                            SELECT 
                                id, nom_entreprise, activite, adresse, code_postal, ville, pays,
                                telephone, fax, email, siret, tva_intracommunautaire, representant_legal_id,
                                logo_url, logo_filename, workspace_id, is_active, created_at, updated_at, created_by
                            FROM entreprises_tiers
                            WHERE id = :id
                            ON CONFLICT (id) DO NOTHING
                        """), {"id": company_id})
                        print(f"✅ Entreprise {company_id} ajoutée à tcompanies")
                    else:
                        print(f"❌ ID {company_id} n'existe nulle part - suppression des références")
                        await conn.execute(text("""
                            DELETE FROM project_company WHERE company_id = :id
                        """), {"id": company_id})
                        print(f"✅ Références orphelines supprimées pour ID {company_id}")
            else:
                print("✅ Aucune donnée orpheline dans project_company")
            
            # 3. Mettre à jour la séquence
            await conn.execute(text("""
                SELECT setval('tcompanies_id_seq', (SELECT MAX(id) FROM tcompanies))
            """))
            
            # 4. Vérifier le résultat final
            result = await conn.execute(text("SELECT COUNT(*) FROM tcompanies"))
            count = result.fetchone()[0]
            print(f"📊 Nombre total d'enregistrements dans tcompanies: {count}")
            
            print("🎉 Nettoyage terminé!")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(fix_orphaned_data())
