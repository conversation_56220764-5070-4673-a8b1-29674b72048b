'use client'

import { useState } from 'react'
// Suppression des icônes non essentielles pour un design sobre

interface ModernHeaderProps {
  title: string
  subtitle?: string
  user?: {
    name: string
    email: string
    avatar?: string
  }
  onLogout?: () => void
}

export default function ModernHeader({ title, subtitle, user, onLogout }: ModernHeaderProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)

  return (
    <header className="bg-white border-b border-ton3 shadow-sm">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side - Title */}
          <div>
            <h1 className="text-2xl font-bold text-ton2">{title}</h1>
            {subtitle && (
              <p className="text-sm text-ton2-light mt-1">{subtitle}</p>
            )}
          </div>

          {/* Right side - Actions - Simplifié */}
          <div className="flex items-center space-x-4">
            {/* Search simplifié sans icône */}
            <div className="hidden md:block relative">
              <input
                type="text"
                placeholder="Rechercher..."
                className="px-4 py-2 w-64 border border-ton3 rounded-lg focus:outline-none focus:ring-2 focus:ring-ton1 focus:border-transparent bg-ton3"
              />
            </div>

            {/* Notifications simplifiées */}
            <div className="relative">
              <button className="px-3 py-2 text-ton2 hover:text-ton1 hover:bg-ton3 rounded-lg transition-colors text-sm font-medium">
                Notifications
              </button>
            </div>

            {/* User menu simplifié */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-ton3 transition-colors"
              >
                <div className="w-8 h-8 bg-ton1 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-sm">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium text-ton2">{user?.name || 'Utilisateur'}</p>
                  <p className="text-xs text-ton2-light">{user?.email || '<EMAIL>'}</p>
                </div>
              </button>

              {/* User dropdown simplifié */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-ton3 z-50">
                  <div className="py-2">
                    <button className="w-full px-4 py-2 text-sm text-ton2 hover:bg-ton3 transition-colors text-left">
                      Mon Profil
                    </button>
                    <button className="w-full px-4 py-2 text-sm text-ton2 hover:bg-ton3 transition-colors text-left">
                      Paramètres
                    </button>
                    <hr className="my-2 border-ton3" />
                    <button
                      onClick={onLogout}
                      className="w-full px-4 py-2 text-sm text-ton1 hover:bg-ton3 transition-colors text-left"
                    >
                      Déconnexion
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
