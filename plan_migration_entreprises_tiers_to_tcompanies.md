# Plan de Migration : EntrepriseTiers → TCompanies

## 🎯 Objectif
<PERSON> progressivement `EntrepriseTiers` vers `TCompanies` avec standardisation complète.

## 📋 Étapes de Migration (Par ordre de priorité)

### Étape 1 : Préparation et Analyse
- [ ] Analyser toutes les références à `EntrepriseTiers`
- [ ] Identifier les fichiers impactés
- [ ] Créer un plan de rollback

### Étape 2 : Nouveau Modèle TCompanies
- [ ] Créer le nouveau modèle `TCompanies` avec colonnes anglaises
- [ ] Garder l'ancien modèle `EntrepriseTiers` temporairement
- [ ] Ajouter les contraintes et types standardisés

### Étape 3 : Migration Base de Données
- [ ] Créer migration Alembic pour renommer la table
- [ ] Renommer `entreprises_tiers` → `tcompanies`
- [ ] Renommer les colonnes (français → anglais)
- [ ] Ajouter les nouvelles contraintes

### Étape 4 : Sch<PERSON><PERSON>ydanti<PERSON>
- [ ] Créer nouveaux schémas `TCompanyCreate`, `TCompanyUpdate`, etc.
- [ ] Ajouter validation française (SIRET, téléphone, code postal)
- [ ] Garder anciens schémas temporairement pour compatibilité

### Étape 5 : API Endpoints
- [ ] Créer nouveaux endpoints `/api/v1/tcompanies/`
- [ ] Migrer la logique métier
- [ ] Garder anciens endpoints avec redirection temporaire

### Étape 6 : Relations et Dépendances
- [ ] Mettre à jour `ProjectCompany` → référence vers `TCompanies`
- [ ] Mettre à jour `TechnicalDocumentCompany`
- [ ] Mettre à jour toutes les foreign keys

### Étape 7 : Tests
- [ ] Migrer les tests CRUD vers `TCompanies`
- [ ] Tester l'isolation multi-tenant
- [ ] Valider les nouvelles contraintes

### Étape 8 : Frontend
- [ ] Mettre à jour les appels API
- [ ] Migrer les composants React
- [ ] Tester l'interface utilisateur

### Étape 9 : Nettoyage
- [ ] Supprimer l'ancien modèle `EntrepriseTiers`
- [ ] Supprimer les anciens endpoints
- [ ] Supprimer les anciens schémas
- [ ] Nettoyer les imports

## 🔍 Fichiers Impactés (Estimation)

### Modèles SQLAlchemy
- `app/models/entreprise_tiers.py` → `app/models/tcompany.py`
- `app/models/project.py` (relations)
- `app/models/document.py` (relations)

### Schémas Pydantic
- `app/schemas/entreprise_tiers.py` → `app/schemas/tcompany.py`

### API Endpoints
- `app/api/api_v1/endpoints/entreprises_tiers.py` → `app/api/api_v1/endpoints/tcompanies.py`
- `app/api/api_v1/api.py` (router)

### CRUD Operations
- `app/crud/entreprise_tiers.py` → `app/crud/tcompany.py`

### Tests
- `tests_crud/test_entreprise_tiers_crud.py` → `tests_crud/test_tcompany_crud.py`

### Frontend
- Tous les composants React utilisant entreprises-tiers
- Appels API dans les hooks et services

## ⚠️ Risques et Précautions

1. **Données existantes** : Migration sans perte de données
2. **Downtime** : Minimiser l'interruption de service
3. **Rollback** : Plan de retour en arrière
4. **Tests** : Validation complète à chaque étape

## 🚀 Prêt pour l'Étape 1 ?

Voulez-vous que je commence par l'**Étape 1 : Préparation et Analyse** ?
Je vais analyser tous les fichiers qui référencent `EntrepriseTiers` pour établir la liste complète des changements nécessaires.
