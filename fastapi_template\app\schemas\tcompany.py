"""
Schémas Pydantic pour TCompany avec validation française
Validation spécialisée pour SIRET, téléphone, code postal français
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime
import re


class TCompanyBase(BaseModel):
    """Schéma de base pour les entreprises tierces"""
    company_name: Optional[str] = Field(None, min_length=1, max_length=255)
    activity: Optional[str] = Field(None, max_length=255)
    address: Optional[str] = None
    postal_code: Optional[str] = Field(None, max_length=10)
    city: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field("France", max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    fax: Optional[str] = Field(None, max_length=20)
    email: Optional[EmailStr] = None
    siret: Optional[str] = Field(None, max_length=14)
    vat_number: Optional[str] = Field(None, max_length=20)
    legal_representative_id: Optional[int] = None
    logo_url: Optional[str] = Field(None, max_length=500)
    logo_filename: Optional[str] = Field(None, max_length=255)
    is_active: Optional[bool] = True


class TCompanyCreate(TCompanyBase):
    """Schéma pour la création d'une entreprise tierce"""
    company_name: str = Field(..., min_length=1, max_length=255, description="Nom de l'entreprise (obligatoire)")
    
    @validator('siret')
    def validate_siret(cls, v):
        """Validation du SIRET français (14 chiffres)"""
        if v is None:
            return v
        
        # Nettoyer le SIRET (supprimer espaces et caractères spéciaux)
        siret_clean = re.sub(r'[^\d]', '', str(v))
        
        if not siret_clean:
            return None
        
        if len(siret_clean) != 14:
            raise ValueError('Le SIRET doit contenir exactement 14 chiffres')
        
        if not siret_clean.isdigit():
            raise ValueError('Le SIRET ne doit contenir que des chiffres')
        
        # Validation de l'algorithme de Luhn pour le SIRET
        if not cls._validate_siret_luhn(siret_clean):
            raise ValueError('Le SIRET n\'est pas valide (échec de la vérification)')
        
        return siret_clean
    
    @validator('phone')
    def validate_phone(cls, v):
        """Validation du numéro de téléphone français"""
        if v is None:
            return v
        
        # Nettoyer le numéro (supprimer espaces, points, tirets)
        phone_clean = re.sub(r'[^\d+]', '', str(v))
        
        if not phone_clean:
            return None
        
        # Formats acceptés :
        # - 0123456789 (10 chiffres commençant par 0)
        # - +33123456789 (12 chiffres commençant par +33)
        # - 33123456789 (11 chiffres commençant par 33)
        
        if phone_clean.startswith('+33'):
            # Format international +33
            if len(phone_clean) != 12:
                raise ValueError('Le numéro international doit contenir 12 chiffres (+33XXXXXXXXX)')
            if not phone_clean[3:].isdigit():
                raise ValueError('Le numéro de téléphone contient des caractères invalides')
            # Vérifier que le premier chiffre après +33 est valide (1-9)
            if phone_clean[3] not in '123456789':
                raise ValueError('Le numéro de téléphone français doit commencer par 01-09 après +33')
        elif phone_clean.startswith('33'):
            # Format international sans +
            if len(phone_clean) != 11:
                raise ValueError('Le numéro international doit contenir 11 chiffres (33XXXXXXXXX)')
            if not phone_clean[2:].isdigit():
                raise ValueError('Le numéro de téléphone contient des caractères invalides')
            if phone_clean[2] not in '123456789':
                raise ValueError('Le numéro de téléphone français doit commencer par 01-09 après 33')
        elif phone_clean.startswith('0'):
            # Format national français
            if len(phone_clean) != 10:
                raise ValueError('Le numéro français doit contenir 10 chiffres (0XXXXXXXXX)')
            if not phone_clean.isdigit():
                raise ValueError('Le numéro de téléphone contient des caractères invalides')
            if phone_clean[1] not in '123456789':
                raise ValueError('Le numéro de téléphone français doit commencer par 01-09')
        else:
            raise ValueError('Le numéro de téléphone doit commencer par 0, 33 ou +33')
        
        return phone_clean
    
    @validator('postal_code')
    def validate_postal_code(cls, v):
        """Validation du code postal français"""
        if v is None:
            return v
        
        # Nettoyer le code postal
        postal_clean = re.sub(r'[^\d]', '', str(v))
        
        if not postal_clean:
            return None
        
        if len(postal_clean) != 5:
            raise ValueError('Le code postal français doit contenir exactement 5 chiffres')
        
        if not postal_clean.isdigit():
            raise ValueError('Le code postal ne doit contenir que des chiffres')
        
        # Vérifier que c'est un code postal français valide (01000-99999, sauf Corse 20000-20999)
        code_int = int(postal_clean)
        if not (1000 <= code_int <= 99999):
            raise ValueError('Le code postal doit être compris entre 01000 et 99999')
        
        return postal_clean
    
    @validator('email')
    def validate_email_domain(cls, v):
        """Validation supplémentaire pour l'email"""
        if v is None:
            return v
        
        # Vérification de la longueur totale (RFC 5321)
        if len(str(v)) > 320:
            raise ValueError('L\'adresse email est trop longue (maximum 320 caractères)')
        
        return v
    
    @validator('vat_number')
    def validate_vat_number(cls, v):
        """Validation du numéro de TVA intracommunautaire"""
        if v is None:
            return v
        
        # Nettoyer le numéro de TVA
        vat_clean = re.sub(r'[^\w]', '', str(v).upper())
        
        if not vat_clean:
            return None
        
        # Format français : FR + 2 chiffres + 9 chiffres (SIRET)
        if vat_clean.startswith('FR'):
            if len(vat_clean) != 13:
                raise ValueError('Le numéro de TVA français doit avoir le format FR + 11 chiffres')
            if not vat_clean[2:].isdigit():
                raise ValueError('Le numéro de TVA français doit contenir uniquement des chiffres après FR')
        else:
            # Autres formats européens (validation basique)
            if len(vat_clean) < 8 or len(vat_clean) > 15:
                raise ValueError('Le numéro de TVA doit contenir entre 8 et 15 caractères')
        
        return vat_clean
    
    @staticmethod
    def _validate_siret_luhn(siret: str) -> bool:
        """Validation de l'algorithme de Luhn pour le SIRET"""
        try:
            # Algorithme de Luhn modifié pour SIRET
            total = 0
            for i, digit in enumerate(siret):
                n = int(digit)
                if i % 2 == 1:  # Position impaire (en partant de 0)
                    n *= 2
                    if n > 9:
                        n = n // 10 + n % 10
                total += n
            return total % 10 == 0
        except (ValueError, TypeError):
            return False


class TCompanyUpdate(BaseModel):
    """Schéma pour la mise à jour d'une entreprise tierce"""
    company_name: Optional[str] = Field(None, min_length=1, max_length=255)
    activity: Optional[str] = Field(None, max_length=255)
    address: Optional[str] = None
    postal_code: Optional[str] = Field(None, max_length=10)
    city: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    fax: Optional[str] = Field(None, max_length=20)
    email: Optional[EmailStr] = None
    siret: Optional[str] = Field(None, max_length=14)
    vat_number: Optional[str] = Field(None, max_length=20)
    legal_representative_id: Optional[int] = None
    logo_url: Optional[str] = Field(None, max_length=500)
    logo_filename: Optional[str] = Field(None, max_length=255)
    is_active: Optional[bool] = None
    
    # Réutiliser les mêmes validators que TCompanyCreate
    _validate_siret = TCompanyCreate.__dict__['validate_siret']
    _validate_phone = TCompanyCreate.__dict__['validate_phone']
    _validate_postal_code = TCompanyCreate.__dict__['validate_postal_code']
    _validate_email_domain = TCompanyCreate.__dict__['validate_email_domain']
    _validate_vat_number = TCompanyCreate.__dict__['validate_vat_number']


class TCompanyResponse(TCompanyBase):
    """Schéma pour la réponse d'une entreprise tierce"""
    id: int
    workspace_id: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[int] = None
    
    # Champs calculés
    full_address: Optional[str] = None
    is_siret_valid: Optional[bool] = None
    
    class Config:
        from_attributes = True
    
    @validator('full_address', always=True)
    def set_full_address(cls, v, values):
        """Calcule l'adresse complète"""
        if v is not None:
            return v
        
        parts = []
        if values.get('address'):
            parts.append(values['address'])
        if values.get('postal_code') and values.get('city'):
            parts.append(f"{values['postal_code']} {values['city']}")
        elif values.get('city'):
            parts.append(values['city'])
        if values.get('country') and values.get('country') != "France":
            parts.append(values['country'])
        
        return ", ".join(parts) if parts else None
    
    @validator('is_siret_valid', always=True)
    def set_siret_valid(cls, v, values):
        """Vérifie la validité du SIRET"""
        if v is not None:
            return v
        
        siret = values.get('siret')
        if not siret:
            return None
        
        return len(siret) == 14 and siret.isdigit() and TCompanyCreate._validate_siret_luhn(siret)


class TCompanyList(BaseModel):
    """Schéma pour la liste paginée des entreprises tierces"""
    items: List[TCompanyResponse]
    total: int
    page: int = 1
    size: int = 50
    pages: int = 1


class TCompanyStats(BaseModel):
    """Schéma pour les statistiques des entreprises tierces"""
    total_companies: int
    active_companies: int
    inactive_companies: int
    companies_with_siret: int
    companies_by_activity: dict
    recent_companies: int  # Créées dans les 30 derniers jours


# Schémas de compatibilité (à supprimer après migration)
class EntrepriseTiersCreate(TCompanyCreate):
    """Schéma de compatibilité - à supprimer après migration"""
    pass

class EntrepriseTiersUpdate(TCompanyUpdate):
    """Schéma de compatibilité - à supprimer après migration"""
    pass

class EntrepriseTiersResponse(TCompanyResponse):
    """Schéma de compatibilité - à supprimer après migration"""
    pass

class EntrepriseTiersList(TCompanyList):
    """Schéma de compatibilité - à supprimer après migration"""
    pass

class EntrepriseTiersStats(TCompanyStats):
    """Schéma de compatibilité - à supprimer après migration"""
    pass
