'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { BusinessDataService } from '@/lib/auth'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import ProtectedRoute from '@/components/ProtectedRoute'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import EntrepriseModal from '@/components/EntrepriseModal'
import { PlusIcon, MagnifyingGlassIcon, BuildingOfficeIcon, PhoneIcon, EnvelopeIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'

interface EntrepriseTiers {
  id: number
  nom_entreprise: string
  activite?: string
  adresse?: string
  code_postal?: string
  ville?: string
  pays?: string
  telephone?: string
  fax?: string
  email?: string
  siret?: string
  tva_intracommunautaire?: string
  representant_legal_nom?: string
  representant_legal_email?: string
  logo_url?: string
  logo_filename?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

interface EntrepriseFormData {
  nom_entreprise: string
  activite: string
  adresse: string
  code_postal: string
  ville: string
  pays: string
  telephone: string
  fax: string
  email: string
  siret: string
  tva_intracommunautaire: string
  logo_url?: string
  logo_filename?: string
}

export default function EntreprisesPage() {
  const { user, signOut } = useAuth()
  const [entreprises, setEntreprises] = useState<EntrepriseTiers[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedEntreprise, setSelectedEntreprise] = useState<EntrepriseTiers | null>(null)
  const [formData, setFormData] = useState<EntrepriseFormData>({
    nom_entreprise: '',
    activite: '',
    adresse: '',
    code_postal: '',
    ville: '',
    pays: 'France',
    telephone: '',
    fax: '',
    email: '',
    siret: '',
    tva_intracommunautaire: '',
    logo_url: '',
    logo_filename: ''
  })

  // Fonction de déconnexion
  const handleLogout = () => {
    signOut()
  }

  // Charger les entreprises
  const fetchEntreprises = async () => {
    try {
      setLoading(true)
      const response = await BusinessDataService.makeAuthenticatedRequest('/entreprises-tiers/')
      
      if (!response.ok) {
        throw new Error('Erreur lors du chargement des entreprises')
      }
      
      const data = await response.json()
      setEntreprises(Array.isArray(data) ? data : [])
      setError(null)
    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors du chargement des entreprises')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEntreprises()
  }, [])

  // Filtrer les entreprises
  const filteredEntreprises = entreprises.filter(entreprise =>
    entreprise.nom_entreprise.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (entreprise.email && entreprise.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (entreprise.ville && entreprise.ville.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (entreprise.siret && entreprise.siret.includes(searchTerm))
  )

  // Gérer la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const endpoint = selectedEntreprise 
        ? `/entreprises-tiers/${selectedEntreprise.id}`
        : '/entreprises-tiers/'
      
      const method = selectedEntreprise ? 'PUT' : 'POST'
      
      const response = await BusinessDataService.makeAuthenticatedRequest(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la sauvegarde')
      }

      // Recharger la liste
      await fetchEntreprises()
      
      // Fermer les modales et réinitialiser
      setIsCreateModalOpen(false)
      setIsEditModalOpen(false)
      setSelectedEntreprise(null)
      resetForm()
      
    } catch (err) {
      console.error('Erreur:', err)
      setError('Erreur lors de la sauvegarde')
    }
  }

  // Réinitialiser le formulaire
  const resetForm = () => {
    setFormData({
      nom_entreprise: '',
      activite: '',
      adresse: '',
      code_postal: '',
      ville: '',
      pays: 'France',
      telephone: '',
      fax: '',
      email: '',
      siret: '',
      tva_intracommunautaire: '',
      logo_url: '',
      logo_filename: ''
    })
  }

  // Ouvrir le modal d'édition
  const handleEdit = (entreprise: EntrepriseTiers) => {
    setSelectedEntreprise(entreprise)
    setFormData({
      nom_entreprise: entreprise.nom_entreprise || '',
      activite: entreprise.activite || '',
      adresse: entreprise.adresse || '',
      code_postal: entreprise.code_postal || '',
      ville: entreprise.ville || '',
      pays: entreprise.pays || 'France',
      telephone: entreprise.telephone || '',
      fax: entreprise.fax || '',
      email: entreprise.email || '',
      siret: entreprise.siret || '',
      tva_intracommunautaire: entreprise.tva_intracommunautaire || '',
      logo_url: entreprise.logo_url || '',
      logo_filename: entreprise.logo_filename || ''
    })
    setIsEditModalOpen(true)
  }

  // Ouvrir le modal de création
  const handleCreate = () => {
    resetForm()
    setSelectedEntreprise(null)
    setIsCreateModalOpen(true)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-ton4">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Carnet d'adresses"
                subtitle="Chargement des entreprises..."
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Chargement des entreprises...</p>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-ton4">
          <div className="flex">
            <ModernSidebar user={user ? {
              name: `${user.first_name} ${user.last_name}`,
              email: user.email
            } : undefined} />

            <div className="flex-1 lg:ml-72">
              <ModernHeader
                title="Carnet d'adresses"
                subtitle="Erreur de chargement"
                user={user ? {
                  name: `${user.first_name} ${user.last_name}`,
                  email: user.email
                } : undefined}
                onLogout={handleLogout}
              />

              <main className="p-6">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Erreur de chargement</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <Button onClick={fetchEntreprises}>Réessayer</Button>
                </div>
              </main>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-ton4">
        <div className="flex">
          <ModernSidebar user={user ? {
            name: `${user.first_name} ${user.last_name}`,
            email: user.email
          } : undefined} />

          <div className="flex-1 lg:ml-72">
            <ModernHeader
              title="Carnet d'adresses"
              subtitle={`Gestion de vos entreprises tierces • ${filteredEntreprises.length} entreprise${filteredEntreprises.length > 1 ? 's' : ''}`}
              user={user ? {
                name: `${user.first_name} ${user.last_name}`,
                email: user.email
              } : undefined}
              onLogout={handleLogout}
            />

            <main className="p-6">
              {/* Barre d'actions */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Rechercher une entreprise..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <Button onClick={handleCreate} className="flex items-center gap-2">
                  <PlusIcon className="h-5 w-5" />
                  Nouvelle entreprise
                </Button>
              </div>

              {/* Liste des entreprises */}
              {filteredEntreprises.length === 0 ? (
                <div className="text-center py-12">
                  <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune entreprise</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm ? 'Aucune entreprise ne correspond à votre recherche.' : 'Commencez par ajouter une entreprise tierce.'}
                  </p>
                  {!searchTerm && (
                    <div className="mt-6">
                      <Button onClick={handleCreate}>
                        <PlusIcon className="h-5 w-5 mr-2" />
                        Ajouter une entreprise
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredEntreprises.map((entreprise) => (
                    <Card key={entreprise.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleEdit(entreprise)}>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          {/* Logo de l'entreprise */}
                          {entreprise.logo_url ? (
                            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                              <Image
                                src={`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${entreprise.logo_url}`}
                                alt={`Logo ${entreprise.nom_entreprise}`}
                                width={48}
                                height={48}
                                className="w-full h-full object-contain"
                                onError={(e) => {
                                  console.error('Erreur de chargement du logo:', entreprise.logo_url)
                                }}
                              />
                            </div>
                          ) : (
                            <BuildingOfficeIcon className="h-12 w-12 text-primary-600 flex-shrink-0" />
                          )}

                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">
                              {entreprise.nom_entreprise}
                            </h3>
                            {entreprise.activite && (
                              <p className="text-sm text-gray-600 mb-3">{entreprise.activite}</p>
                            )}
                          </div>
                        </div>
                        <div className={`px-2 py-1 text-xs rounded-full ${
                          entreprise.is_active
                            ? 'bg-ton3 text-ton1'
                            : 'bg-ton3-dark text-ton2'
                        }`}>
                          {entreprise.is_active ? 'Actif' : 'Inactif'}
                        </div>
                      </div>

                      <div className="space-y-2">
                        {entreprise.adresse && (
                          <div className="text-sm text-gray-600">
                            <p>{entreprise.adresse}</p>
                            {(entreprise.code_postal || entreprise.ville) && (
                              <p>{entreprise.code_postal} {entreprise.ville}</p>
                            )}
                          </div>
                        )}

                        {entreprise.telephone && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <PhoneIcon className="h-4 w-4" />
                            <span>{entreprise.telephone}</span>
                          </div>
                        )}

                        {entreprise.email && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <EnvelopeIcon className="h-4 w-4" />
                            <span>{entreprise.email}</span>
                          </div>
                        )}

                        {entreprise.siret && (
                          <div className="text-xs text-gray-500">
                            SIRET: {entreprise.siret}
                          </div>
                        )}

                        {entreprise.representant_legal_nom && (
                          <div className="text-xs text-gray-500">
                            Représentant: {entreprise.representant_legal_nom}
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </main>
          </div>
        </div>

        {/* Modales */}
        <EntrepriseModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={false}
        />

        <EntrepriseModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSubmit={handleSubmit}
          formData={formData}
          setFormData={setFormData}
          isEdit={true}
          entrepriseId={selectedEntreprise?.id}
          onRefresh={fetchEntreprises}
        />
      </div>
    </ProtectedRoute>
  )
}
