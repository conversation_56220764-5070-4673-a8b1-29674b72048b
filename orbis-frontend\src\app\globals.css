@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System Variables - Système de couleurs 4 tons */
:root {
  /* Système de couleurs principal - 4 tons */
  --ton1: #0F766E;  /* Vert principal - pour les éléments actifs/primaires */
  --ton2: #333333;  /* Gris foncé - pour le texte et éléments secondaires */
  --ton3: #F8F9FA;  /* Gris très clair - pour les arrière-plans et états hover */
  --ton4: #FFFFFF;  /* Blanc pur - pour les cartes et contrastes */

  /* Couleurs dérivées pour les nuances */
  --ton1-light: #14b8a6;    /* Version plus claire de ton1 */
  --ton1-dark: #0d5c55;     /* Version plus foncée de ton1 */
  --ton2-light: #666666;    /* Version plus claire de ton2 */
  --ton2-dark: #1a1a1a;     /* Version plus foncée de ton2 */
  --ton3-dark: #E9ECEF;     /* Version plus foncée de ton3 */

  /* Alias pour compatibilité */
  --white: var(--ton4);

  /* Ombres modernes */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Rayons de bordure */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Styles de cartes modernes - Système 3 tons */
.modern-card {
  background-color: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--ton3);
  transition: all 0.3s ease;
}

.modern-card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--ton3-dark);
}

.card-primary {
  background-color: var(--ton1);
  color: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.card-primary:hover {
  background-color: var(--ton1-light);
  box-shadow: var(--shadow-xl);
}

.card-secondary {
  background-color: var(--ton3);
  color: var(--ton2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card-secondary:hover {
  background-color: var(--ton3-dark);
  box-shadow: var(--shadow-lg);
}

/* Styles de sidebar - Système 3 tons */
.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  color: var(--ton2);
}

.sidebar-item.active {
  background-color: var(--ton3);
  color: var(--ton1);
  border-left: 4px solid var(--ton1);
  padding-left: 0.75rem;
}

.sidebar-item:not(.active):hover {
  background-color: var(--ton3);
  color: var(--ton1);
}

/* Styles de boutons modernes - Système 3 tons */
.btn-modern {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  outline: none;
}

.btn-modern:focus {
  box-shadow: 0 0 0 2px var(--ton3), 0 0 0 4px var(--ton1);
}

.btn-primary {
  background-color: var(--ton1);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--ton1-light);
}

.btn-secondary {
  background-color: var(--ton3);
  color: var(--ton2);
}

.btn-secondary:hover {
  background-color: var(--ton3-dark);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

/* Modern Sidebar */
.sidebar-modern {
  @apply bg-white border-r border-gray-200 shadow-lg;
}

.sidebar-item {
  @apply flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200 rounded-lg mx-2;
}

.sidebar-item.active {
  @apply bg-primary-100 text-primary-700 font-medium;
}

/* Modern Typography */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}



/* Base Styles */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    color: var(--ton2);
    background-color: var(--white);
  }

  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--ton2);
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ton2);
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--ton2);
    margin-bottom: 0.75rem;
  }

  h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--ton2);
    margin-bottom: 0.5rem;
  }
}

/* Classes utilitaires pour le système 3 tons */
@layer utilities {
  /* Couleurs de texte */
  .text-ton1 { color: var(--ton1); }
  .text-ton1-light { color: var(--ton1-light); }
  .text-ton1-dark { color: var(--ton1-dark); }
  .text-ton2 { color: var(--ton2); }
  .text-ton2-light { color: var(--ton2-light); }
  .text-ton2-dark { color: var(--ton2-dark); }
  .text-ton3 { color: var(--ton3); }
  .text-ton3-dark { color: var(--ton3-dark); }
  .text-ton4 { color: var(--ton4); }

  /* Couleurs d'arrière-plan */
  .bg-ton1 { background-color: var(--ton1); }
  .bg-ton1-light { background-color: var(--ton1-light); }
  .bg-ton1-dark { background-color: var(--ton1-dark); }
  .bg-ton2 { background-color: var(--ton2); }
  .bg-ton2-light { background-color: var(--ton2-light); }
  .bg-ton2-dark { background-color: var(--ton2-dark); }
  .bg-ton3 { background-color: var(--ton3); }
  .bg-ton3-dark { background-color: var(--ton3-dark); }
  .bg-ton4 { background-color: var(--ton4); }

  /* Couleurs de bordure */
  .border-ton1 { border-color: var(--ton1); }
  .border-ton1-light { border-color: var(--ton1-light); }
  .border-ton1-dark { border-color: var(--ton1-dark); }
  .border-ton2 { border-color: var(--ton2); }
  .border-ton2-light { border-color: var(--ton2-light); }
  .border-ton2-dark { border-color: var(--ton2-dark); }
  .border-ton3 { border-color: var(--ton3); }
  .border-ton3-dark { border-color: var(--ton3-dark); }
  .border-ton4 { border-color: var(--ton4); }
}

/* Component Styles - Système 3 tons */
@layer components {
  .card {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--ton3);
    box-shadow: var(--shadow-md);
    transition: box-shadow 0.3s ease;
    overflow: hidden;
  }

  .card:hover {
    box-shadow: var(--shadow-lg);
  }

  .card-header {
    padding: 1rem;
    border-bottom: 1px solid var(--ton3);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-body {
    padding: 1rem;
  }

  .card-footer {
    padding: 1rem;
    background-color: var(--ton3);
    border-top: 1px solid var(--ton3-dark);
  }

  .form-input {
    border-radius: var(--radius-md);
    border: 1px solid var(--ton3);
    box-shadow: var(--shadow-sm);
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--ton1);
    box-shadow: 0 0 0 2px var(--ton3), 0 0 0 4px var(--ton1);
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--ton2);
    margin-bottom: 0.25rem;
  }

  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-primary {
    background-color: var(--ton3);
    color: var(--ton1);
  }

  .badge-success {
    background-color: var(--ton3);
    color: var(--ton1);
  }

  .badge-warning {
    background-color: var(--ton3-dark);
    color: var(--ton2);
  }

  .badge-danger {
    background-color: var(--ton3-dark);
    color: var(--ton2-dark);
  }
}

/* Custom scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 5px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation Delay Utilities */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: #fff;
    color: #000;
  }
  
  .print-break-inside-avoid {
    break-inside: avoid;
  }
}